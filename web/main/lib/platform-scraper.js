"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformScraper = exports.LoginRequiredError = void 0;
const utils_1 = require("./utils"); // 引入 retry 工具
const config_1 = require("../config");
/**
 * V2.0 专用错误类：登录需求错误
 * 当检测到需要登录时抛出，触发自动登录流程
 */
class LoginRequiredError extends Error {
    constructor(platform, reason, currentUrl) {
        super(`Login required for ${platform}: ${reason}`);
        this.name = 'LoginRequiredError';
        this.platform = platform;
        this.reason = reason;
        this.currentUrl = currentUrl;
    }
}
exports.LoginRequiredError = LoginRequiredError;
// --- PLATFORM-SPECIFIC SELECTORS ---
// These are the most fragile parts of any scraper and MUST be updated if the target website's layout changes.
const SELECTORS = {
    taobao: {
        // 首页搜索相关
        homepageSearchInput: '#q',
        homepageSearchButton: '.btn-search',
        // 登录检测相关
        loginForm: '#login-form',
        loginQRCode: '.qrcode-img',
        captchaSlider: '#nc_1_n1z',
        loginButton: '.login-btn',
        userAvatar: '.site-nav-user',
        // 🚀 更新：基于2024年最新页面结构的搜索结果选择器
        searchResultItem: '.item, .Card--itemCard, .grid-item, .product-item, [data-category="auctions"] .item',
        searchResultLink: 'a[href*="item.taobao.com"], a[href*="detail.tmall.com"], .item-title-link, .Card--doubleCardWrapper--L2Xq3oD, .title > a',
        productTitle: '.title, .item-title, .Card--title',
        productPrice: '.price, .Price--priceReal, .Card--price',
        productSales: '.deal-cnt, .Price--dealCount--yIoPkeG, .sale-count, .Card--sales',
        // 🚀 修复：基于实际页面结构的排序选项相关选择器
        sortTabs: '.sort-bar, .sort-tabs, .filter-bar, .sort-options, [class*="sort"], [class*="filter"]',
        salesSortTab: 'a:has-text("销量"), span:has-text("销量"), div:has-text("销量"), [data-value="sale-desc"], .sort-item[data-value="sale-desc"], .item[data-value="sale-desc"], .tab[data-sort="sale-desc"]',
        activeSortTab: '.selected, .active, [class*="selected"], [class*="active"]',
        // 评论相关
        commentTab: 'li[data-anchor^="J_Reviews"]',
        commentItem: '.tm-rate-item',
        commentContent: '.tm-rate-fulltxt',
    },
    xiaohongshu: {
        // 首页搜索相关
        homepageSearchInput: 'input[placeholder*="搜索"], textbox[placeholder*="搜索"]',
        // 登录检测相关
        loginPopup: '.login-container, .login-popup',
        loginButton: 'button:has-text("登录"), .login-btn',
        userProfile: 'a[href*="/user/profile"], .user-info',
        // 🚀 更新：基于实际浏览器观察的小红书选择器（修复无效选择器）
        searchResultItem: 'section.note-item, .note-item, .search-item, .feeds-page section, section[class*="note"]',
        searchResultLink: 'a[href*="/search_result/"], a[href*="/explore/"], a.cover, .note-link, a[href*="/discovery/item/"]',
        postTitle: '.title, .note-title, .content-title',
        postLikes: '.like-count, .engagement-count, .stats',
        postAuthor: '.author, .user-name',
        // 🚀 更新：基于实际浏览器测试的排序选择器
        sortTabs: 'listitem, .filter-item, .sort-option',
        hotSortTab: 'listitem:has-text("最热"), listitem:has-text("最多评论"), .filter-item:has-text("最热")',
        activeSortTab: 'listitem.selected, .selected, .active',
        // 评论相关
        commentSection: '#comment-container, .comment-section',
        commentItem: '.comment-item, .comment',
        commentContent: '.comment-content, .comment-text',
    }
};
const URLS = {
    taobao: {
        // 从首页开始，不再直接访问搜索URL
        homepage: 'https://www.taobao.com',
        // 搜索URL生成器仅用于参考，实际不直接访问
        search: (keyword) => `https://s.taobao.com/search?q=${encodeURIComponent(keyword)}&sort=sale-desc&s=0`,
    },
    xiaohongshu: {
        // 小红书首页
        homepage: 'https://www.xiaohongshu.com',
        // 小红书搜索URL - Web版可能默认已经按热度排序
        search: (keyword) => `https://www.xiaohongshu.com/search_result?keyword=${encodeURIComponent(keyword)}&source=web_explore_feed`,
    }
};
/**
 * PlatformScraper encapsulates the logic for scraping a single page on a specific platform.
 * It handles link discovery, comment scraping, and checking for interruptions like login/captcha.
 */
class PlatformScraper {
    constructor(page, platform) {
        this.antiDetectionInitialized = false;
        this.page = page;
        this.platform = platform;
        this.selectors = SELECTORS[this.platform];
    }
    /**
     * 🚀 增强反爬虫检测初始化 - 全面伪装为真实用户
     */
    async initializeAntiDetection() {
        // 避免重复初始化
        if (this.antiDetectionInitialized) {
            return;
        }
        try {
            console.log(`[PlatformScraper] 🎭 初始化增强反爬虫策略 - ${this.platform}`);
            // 1. 设置真实视口大小
            await this.page.setViewportSize({ width: 1366, height: 768 });
            // 2. 设置真实浏览器请求头
            await this.page.setExtraHTTPHeaders({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0'
            });
            // 3. 注入反检测脚本 - 删除自动化痕迹
            await this.page.addInitScript(() => {
                // 删除 webdriver 属性
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                // 伪造 chrome 对象
                window.chrome = {
                    runtime: {},
                    loadTimes: function () { },
                    csi: function () { },
                    app: {}
                };
                // 伪造 plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                // 伪造 languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });
                // 删除自动化相关属性
                delete window.__nightmare;
                delete window.__phantomas;
                delete window.callPhantom;
                delete window._phantom;
                delete window.phantom;
                console.log('🎭 反检测脚本已注入');
            });
            // 4. 监听页面关闭/崩溃事件
            this.page.on('close', () => {
                console.log(`[PlatformScraper] ⚠️  ${this.platform} 页面被关闭`);
            });
            this.page.on('crash', () => {
                console.log(`[PlatformScraper] 💥 ${this.platform} 页面崩溃`);
            });
            // 5. 设置页面错误监听
            this.page.on('pageerror', (error) => {
                console.log(`[PlatformScraper] 📄 ${this.platform} 页面错误:`, error.message);
            });
            this.antiDetectionInitialized = true;
            console.log(`[PlatformScraper] ✅ ${this.platform} 反爬虫策略初始化完成`);
        }
        catch (error) {
            console.error(`[PlatformScraper] ❌ ${this.platform} 反爬虫初始化失败:`, error);
            // 不抛出错误，继续执行
        }
    }
    /**
     * V2.0 增强登录和页面状态检测 - 支持骨架屏、风控页面、页面关闭检测
     * 检测到需要登录时抛出 LoginRequiredError 触发自动登录流程
     * @throws {LoginRequiredError} 当检测到需要登录时
     */
    async isLoginRequired() {
        try {
            // 1. 检查页面是否被关闭或无效
            if (this.page.isClosed()) {
                console.log(`[PlatformScraper] ❌ ${this.platform} 页面已被关闭`);
                throw new Error(`${this.platform} 页面被意外关闭，可能是反爬虫检测导致。请尝试：1) 检查网络连接 2) 重新启动任务 3) 手动登录后重试`);
            }
            // 2. 检查当前页面URL，识别风控和拒绝访问页面
            const currentUrl = this.page.url();
            console.log(`[PlatformScraper] 🔍 检查页面状态 - URL: ${currentUrl}`);
            // 淘宝风控页面检测
            if (this.platform === 'taobao' && (currentUrl.includes('deny_h5.html') ||
                currentUrl.includes('sec.taobao.com') ||
                currentUrl.includes('login.taobao.com') ||
                currentUrl.includes('verification'))) {
                console.log(`[PlatformScraper] ⚠️  检测到淘宝风控/登录页面: ${currentUrl}`);
                throw new LoginRequiredError(this.platform, '检测到风控或登录页面', currentUrl);
            }
            // 小红书登录页面检测
            if (this.platform === 'xiaohongshu' && (currentUrl.includes('login') ||
                currentUrl.includes('signin') ||
                currentUrl.includes('auth'))) {
                console.log(`[PlatformScraper] ⚠️  检测到小红书登录页面: ${currentUrl}`);
                throw new LoginRequiredError(this.platform, '检测到登录页面', currentUrl);
            }
            // 3. 检查页面内容是否为骨架屏或空白
            const isSkeletonScreen = await this.detectSkeletonScreen();
            if (isSkeletonScreen) {
                console.log(`[PlatformScraper] ⚠️  检测到${this.platform}骨架屏，页面内容未完全加载`);
                // 骨架屏不一定需要登录，可能只是加载慢，给更多时间
                await this.waitForContentLoad();
                return false;
            }
            // 4. 检查具体的登录元素
            const selector = this.platform === 'taobao'
                ? SELECTORS.taobao.loginForm
                : SELECTORS.xiaohongshu.loginPopup;
            if (selector) {
                const loginElement = await this.page.$(selector);
                if (loginElement) {
                    const isVisible = await loginElement.isVisible();
                    if (isVisible) {
                        console.log(`[PlatformScraper] ⚠️  检测到${this.platform}登录表单，需要登录`);
                        throw new LoginRequiredError(this.platform, '检测到登录表单', currentUrl);
                    }
                }
            }
            // 5. 🚀 修复：更精确的登录状态检测，避免误判
            if (this.platform === 'xiaohongshu') {
                // 🔧 修复：更准确的小红书登录状态检测
                try {
                    // 检查是否有用户头像或用户相关元素
                    const userElements = await this.page.$$('img[alt*="头像"], img[alt*="avatar"], .user-avatar, .avatar, .user-info, .header-user');
                    const userLinks = await this.page.$$('a[href*="/user/"], a[href*="/profile/"], a[href*="/u/"]');
                    // 检查是否有登录按钮（更明确的登录需求指示）
                    const loginButtons = await this.page.$$('button:has-text("登录"), .login-btn, .sign-in-btn, [class*="login"]');
                    console.log(`[PlatformScraper] 🔍 小红书登录状态检测: 用户元素=${userElements.length}, 用户链接=${userLinks.length}, 登录按钮=${loginButtons.length}`);
                    // 如果有明显的登录按钮，说明需要登录
                    if (loginButtons.length > 0) {
                        console.log(`[PlatformScraper] ⚠️  小红书检测到登录按钮，需要登录`);
                        throw new LoginRequiredError(this.platform, '检测到登录按钮', currentUrl);
                    }
                    // 如果有用户相关元素，说明已登录
                    if (userElements.length > 0 || userLinks.length > 0) {
                        console.log(`[PlatformScraper] ✅ 小红书检测到用户元素，判断为已登录状态`);
                        return false; // 有用户元素，说明已登录
                    }
                    // 检查页面文本中的登录提示
                    const pageText = await this.page.textContent('body');
                    if (pageText) {
                        const strongLoginKeywords = ['请先登录', '需要登录', '登录后查看', '未登录', '立即登录', '登录小红书'];
                        const hasStrongLoginText = strongLoginKeywords.some(keyword => pageText.includes(keyword));
                        if (hasStrongLoginText) {
                            console.log(`[PlatformScraper] ⚠️  小红书页面包含强登录提示文本，需要登录`);
                            throw new LoginRequiredError(this.platform, '页面包含强登录提示文本', currentUrl);
                        }
                    }
                    // 🔧 修复：检查URL是否重定向到登录页面
                    if (currentUrl.includes('signin') || currentUrl.includes('login') || currentUrl.includes('auth')) {
                        console.log(`[PlatformScraper] ⚠️  小红书URL重定向到登录页面: ${currentUrl}`);
                        throw new LoginRequiredError(this.platform, 'URL重定向到登录页面', currentUrl);
                    }
                }
                catch (error) {
                    if (error instanceof LoginRequiredError) {
                        throw error; // 重新抛出登录需求错误
                    }
                    console.warn(`[PlatformScraper] ⚠️ 小红书登录状态检测过程中出错:`, error);
                    // 检测出错时，保守地假设需要登录
                    throw new LoginRequiredError(this.platform, '登录状态检测失败', currentUrl);
                }
            }
            else if (this.platform === 'taobao') {
                // 淘宝特定检测：检查页面文本
                const pageText = await this.page.textContent('body');
                if (pageText) {
                    const loginKeywords = ['请先登录', '需要登录', '登录淘宝'];
                    const hasLoginText = loginKeywords.some(keyword => pageText.includes(keyword));
                    if (hasLoginText) {
                        console.log(`[PlatformScraper] ⚠️  淘宝页面包含登录相关文本，可能需要登录`);
                        throw new LoginRequiredError(this.platform, '页面包含登录相关文本', currentUrl);
                    }
                }
            }
            console.log(`[PlatformScraper] ✅ ${this.platform} 页面状态正常，无需登录`);
            return false;
        }
        catch (error) {
            console.error(`[PlatformScraper] ❌ ${this.platform} 页面状态检测失败:`, error);
            // 如果是LoginRequiredError，直接重新抛出
            if (error instanceof LoginRequiredError) {
                throw error;
            }
            // 如果是页面关闭错误，重新抛出
            if (error instanceof Error && error.message.includes('页面被意外关闭')) {
                throw error;
            }
            // 其他错误默认认为不需要登录
            return false;
        }
    }
    /**
     * 🚀 骨架屏检测 - 识别页面是否处于加载状态
     */
    async detectSkeletonScreen() {
        try {
            // 1. 检查常见的骨架屏类名
            const skeletonSelectors = [
                '.skeleton',
                '.skeleton-loading',
                '.loading-skeleton',
                '.placeholder-loading',
                '.content-placeholder',
                '[class*="skeleton"]',
                '[class*="loading"]'
            ];
            for (const selector of skeletonSelectors) {
                const skeletonElement = await this.page.$(selector);
                if (skeletonElement && await skeletonElement.isVisible()) {
                    console.log(`[PlatformScraper] 🔍 发现骨架屏元素: ${selector}`);
                    return true;
                }
            }
            // 2. 检查页面内容是否过少（可能是骨架屏）
            const bodyText = await this.page.textContent('body');
            if (!bodyText || bodyText.trim().length < 100) {
                console.log(`[PlatformScraper] 🔍 页面内容过少，可能是骨架屏或加载中`);
                return true;
            }
            // 3. 检查是否有实际的商品/内容元素
            if (this.platform === 'taobao') {
                const hasProducts = await this.page.$(SELECTORS.taobao.searchResultItem);
                if (!hasProducts) {
                    console.log(`[PlatformScraper] 🔍 淘宝页面未发现商品元素，可能是骨架屏`);
                    return true;
                }
            }
            else if (this.platform === 'xiaohongshu') {
                const hasPosts = await this.page.$(SELECTORS.xiaohongshu.searchResultItem);
                if (!hasPosts) {
                    console.log(`[PlatformScraper] 🔍 小红书页面未发现帖子元素，可能是骨架屏`);
                    return true;
                }
            }
            return false;
        }
        catch (error) {
            console.error(`[PlatformScraper] 骨架屏检测失败:`, error);
            return false;
        }
    }
    /**
     * 🚀 等待内容完全加载 - 解决骨架屏问题
     */
    async waitForContentLoad() {
        console.log(`[PlatformScraper] ⏳ 等待${this.platform}页面内容完全加载...`);
        const maxWaitTime = 30000; // 最多等待30秒
        const checkInterval = 2000; // 每2秒检查一次
        let waitTime = 0;
        while (waitTime < maxWaitTime) {
            try {
                // 检查是否还是骨架屏
                const isStillSkeleton = await this.detectSkeletonScreen();
                if (!isStillSkeleton) {
                    console.log(`[PlatformScraper] ✅ ${this.platform}页面内容加载完成`);
                    return;
                }
                // 尝试滚动页面触发内容加载
                await this.page.evaluate(() => {
                    window.scrollTo(0, document.body.scrollHeight / 2);
                });
                await this.page.waitForTimeout(checkInterval);
                waitTime += checkInterval;
                console.log(`[PlatformScraper] ⏳ 继续等待内容加载... (${waitTime / 1000}s/${maxWaitTime / 1000}s)`);
            }
            catch (error) {
                console.error(`[PlatformScraper] 等待内容加载时出错:`, error);
                break;
            }
        }
        if (waitTime >= maxWaitTime) {
            console.log(`[PlatformScraper] ⚠️  等待内容加载超时，继续执行（可能仍是骨架屏状态）`);
        }
    }
    /**
     * 🚀 增强链接发现 - 支持页面有效性检查和智能错误处理
     * 针对淘宝：从首页搜索进入绕过反爬虫，自动切换到销量排序
     * 针对小红书：直接访问搜索URL（如果工作的话）
     * @param {string[]} keywords - The keywords to search for.
     * @returns {Promise<string[]>} - A list of unique URLs from first page only.
     */
    async discoverLinks(keywords) {
        console.log(`[PlatformScraper] 🚀 Starting ${this.platform} link discovery for ${keywords.length} keywords.`);
        if (this.page.isClosed()) {
            throw new Error(`${this.platform} page was closed unexpectedly. Cannot continue.`);
        }
        await this.initializeAntiDetection();
        const allLinks = new Set();
        for (const [index, keyword] of keywords.entries()) {
            try {
                console.log(`[PlatformScraper] 🔍 Processing keyword ${index + 1}/${keywords.length}: "${keyword}"`);
                if (this.page.isClosed()) {
                    console.error(`[PlatformScraper] ❌ Page closed while processing "${keyword}".`);
                    break;
                }
                let links = [];
                if (this.platform === 'taobao') {
                    links = await this.searchOnTaobao(keyword);
                }
                else {
                    links = await this.searchOnXiaohongshu(keyword);
                }
                links.forEach(link => allLinks.add(link));
                console.log(`[PlatformScraper] ✅ Keyword "${keyword}" yielded ${links.length} new links.`);
            }
            catch (error) {
                console.error(`[PlatformScraper] ❌ Failed to process keyword "${keyword}":`, error);
                // In a worker model, we throw the error to let the orchestrator handle it
                // It could be a page crash, a login wall, etc.
                throw error;
            }
            if (index < keywords.length - 1) {
                const delay = Math.random() * 1500 + 500; // 0.5-2s delay
                await this.page.waitForTimeout(delay);
            }
        }
        console.log(`[PlatformScraper] ✅ ${this.platform} discovery complete. Found ${allLinks.size} unique links.`);
        return Array.from(allLinks);
    }
    /**
     * Performs a search on Taobao within the existing page.
     */
    async searchOnTaobao(keyword) {
        try {
            // 🚀 修复：使用正确的淘宝搜索URL格式，避免反爬虫页面
            const searchUrl = `https://s.taobao.com/search?commend=all&ie=utf8&initiative_id=tbindexz_20170306&page=1&preLoadOrigin=https%3A%2F%2Fwww.taobao.com&q=${encodeURIComponent(keyword)}&search_type=item&sourceId=tb.index&spm=a21bo.jianhua%2Fa.search_manual.0&ssid=s5-e&tab=all`;
            console.log(`[PlatformScraper] 🔍 直接导航到淘宝搜索页面: ${searchUrl}`);
            await (0, utils_1.retry)(() => this.page.goto(searchUrl, { waitUntil: 'domcontentloaded', timeout: 30000 }), 3, 2000, `[TaobaoDirectSearch]`);
            // 等待页面稳定
            await this.page.waitForTimeout(2000);
            // Check for login wall or captcha after search
            const loginRequired = await this.isLoginRequired();
            if (loginRequired) {
                console.log('[PlatformScraper] ⚠️ Login required after search. Waiting for user intervention...');
                await this.waitForTaobaoLogin();
                // Re-navigate after login
                await this.page.goto(searchUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
                await this.page.waitForTimeout(2000);
            }
            // 🚀 修复：切换到销量排序
            await this.switchToSalesSorting();
            // 🚀 智能等待搜索结果加载（多选择器策略）
            console.log(`[PlatformScraper] 🔍 淘宝搜索结果页面URL: ${this.page.url()}`);
            // 定义多个可能的商品选择器（按优先级排序）
            const itemSelectors = [
                '.item:visible', // 经典布局
                '.Card--itemCard:visible', // 新版卡片布局
                '[data-category="auctions"] .item:visible', // 拍卖类商品
                '.grid-item:visible', // 网格布局
                '.product-item:visible' // 通用商品项
            ];
            let foundElements = [];
            let workingSelector = '';
            // 尝试找到有效的商品选择器
            for (const selector of itemSelectors) {
                try {
                    await this.page.waitForSelector(selector.replace(':visible', ''), { timeout: 5000 });
                    const elements = await this.page.$$(selector);
                    if (elements.length > 0) {
                        foundElements = elements;
                        workingSelector = selector;
                        console.log(`[PlatformScraper] ✅ 使用选择器 "${selector}" 找到 ${elements.length} 个商品`);
                        break;
                    }
                }
                catch {
                    console.log(`[PlatformScraper] ⚠️ 选择器 "${selector}" 未找到元素，尝试下一个`);
                }
            }
            if (foundElements.length === 0) {
                console.log(`[PlatformScraper] ❌ 所有选择器都未找到商品，页面可能需要登录或存在反爬虫`);
                const pageTitle = await this.page.title();
                console.log(`[PlatformScraper] 页面标题: ${pageTitle}`);
                return [];
            }
            // 🚀 增强的链接提取逻辑
            const pageLinks = await this.page.$$eval(workingSelector, (items) => {
                return items.map((item) => {
                    // 多种链接查找策略
                    const linkSelectors = [
                        'a[href*="item.taobao.com"]',
                        'a[href*="detail.tmall.com"]',
                        '.item-title-link',
                        '.Card--doubleCardWrapper--L2Xq3oD',
                        '.title > a',
                        'a[href*="/item.htm"]'
                    ];
                    for (const linkSelector of linkSelectors) {
                        const link = item.querySelector(linkSelector);
                        if (link?.href) {
                            return link.href;
                        }
                    }
                    // 最后尝试：查找任何包含商品ID的链接
                    const anyLink = item.querySelector('a[href*="id="]');
                    return anyLink?.href;
                }).filter(Boolean);
            });
            console.log(`[PlatformScraper] 🔍 提取到 ${pageLinks.length} 个原始链接`);
            // 过滤和验证链接
            const scrapingConfig = (0, config_1.getScrapingConfig)();
            const filteredLinks = pageLinks
                .filter((link) => link && (link.includes('item.taobao.com') ||
                link.includes('detail.tmall.com') ||
                (link.includes('taobao.com') && link.includes('id='))))
                .slice(0, scrapingConfig.taobaoMaxLinksPerPage); // 🔧 使用配置化的链接数量限制
            console.log(`[PlatformScraper] 🔍 过滤后得到 ${filteredLinks.length} 个有效链接`);
            return filteredLinks;
        }
        catch (error) {
            console.error(`[PlatformScraper] ❌ Taobao search failed for keyword: "${keyword}"`, error);
            throw error; // Propagate error
        }
    }
    /**
     * Performs a search on Xiaohongshu within the existing page.
     */
    async searchOnXiaohongshu(keyword) {
        try {
            // 🚀 修复：检查页面是否已关闭
            if (this.page.isClosed()) {
                throw new Error('xiaohongshu page was closed unexpectedly. Cannot continue.');
            }

            // 🚀 修复：使用更准确的小红书搜索URL
            const searchUrl = `https://www.xiaohongshu.com/search_result?keyword=${encodeURIComponent(keyword)}&source=web_explore_feed`;
            console.log(`[PlatformScraper] 🔍 小红书搜索URL: ${searchUrl}`);

            // 🚀 修复：增加页面状态检查
            try {
                await (0, utils_1.retry)(() => this.page.goto(searchUrl, { waitUntil: 'domcontentloaded', timeout: 30000 }), 3, 2000, `[XHSGoToSearch]`);
                await this.page.waitForTimeout(5000); // 增加等待时间，确保SPA完全加载

                // 检查页面是否正常加载
                const pageTitle = await this.page.title();
                console.log(`[PlatformScraper] 小红书页面标题: ${pageTitle}`);

                if (pageTitle.includes('404') || pageTitle.includes('错误')) {
                    throw new Error(`小红书页面加载失败: ${pageTitle}`);
                }
            } catch (navError) {
                console.error(`[PlatformScraper] 小红书页面导航失败:`, navError);
                throw new Error(`小红书页面导航失败: ${navError.message}`);
            }
            const loginRequired = await this.isLoginRequired();
            if (loginRequired) {
                console.log('[PlatformScraper] ⚠️ Login required. Waiting for user intervention...');
                await this.waitForXiaohongshuLogin();
                // Re-navigate after login
                await this.page.goto(searchUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
                await this.page.waitForTimeout(3000);
            }
            await this.switchToHotSorting();
            console.log(`[PlatformScraper] 🔍 小红书搜索结果页面URL: ${this.page.url()}`);
            // 🚀 多选择器策略查找笔记元素
            const noteSelectors = [
                'section.note-item', // 标准笔记项
                '.note-item', // 简化笔记项
                '.search-item', // 搜索结果项
                'a[href*="/search_result/"]', // 直接链接
                '.feeds-page section', // Feed页面section
                '[data-v-*] section' // Vue组件section
            ];
            let foundElements = [];
            let workingSelector = '';
            // 尝试找到有效的笔记选择器
            for (const selector of noteSelectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 5000 });
                    const elements = await this.page.$$(selector);
                    if (elements.length > 0) {
                        foundElements = elements;
                        workingSelector = selector;
                        console.log(`[PlatformScraper] ✅ 使用选择器 "${selector}" 找到 ${elements.length} 个笔记`);
                        break;
                    }
                }
                catch {
                    console.log(`[PlatformScraper] ⚠️ 选择器 "${selector}" 未找到元素，尝试下一个`);
                }
            }
            if (foundElements.length === 0) {
                console.log(`[PlatformScraper] ❌ 所有选择器都未找到笔记，页面可能需要登录`);
                const pageTitle = await this.page.title();
                console.log(`[PlatformScraper] 页面标题: ${pageTitle}`);
                return [];
            }
            // 🚀 修复：使用$$eval而不是$eval，并改进链接提取逻辑
            const pageLinks = await this.page.$$eval(workingSelector, (items) => {
                return items.map((item) => {
                    // 多种链接查找策略
                    const linkSelectors = [
                        'a[href*="/search_result/"]', // 搜索结果链接
                        'a[href*="/explore/"]', // 探索页面链接
                        'a.cover', // 封面链接
                        '.note-link', // 笔记链接
                        'a[href*="/discovery/item/"]', // 发现页面链接
                        'a' // 任何链接
                    ];
                    for (const linkSelector of linkSelectors) {
                        const link = item.querySelector(linkSelector);
                        if (link?.href && link.href.includes('xiaohongshu.com')) {
                            return link.href;
                        }
                    }
                    return null;
                }).filter((link) => Boolean(link));
            });
            console.log(`[PlatformScraper] 🔍 提取到 ${pageLinks.length} 个原始链接`);
            // 🚀 修复：更宽松的链接过滤条件
            const scrapingConfig = (0, config_1.getScrapingConfig)();
            const filteredLinks = pageLinks
                .filter((link) => link && (link.includes('/search_result/') ||
                link.includes('/explore/') ||
                link.includes('/discovery/item/') ||
                (link.includes('xiaohongshu.com') && (link.includes('/item/') || link.includes('/note/')))))
                .slice(0, scrapingConfig.xiaohongshuMaxLinksPerPage); // 🔧 使用配置化的链接数量限制
            console.log(`[PlatformScraper] 🔍 过滤后得到 ${filteredLinks.length} 个有效链接`);
            return filteredLinks;
        }
        catch (error) {
            console.error(`[PlatformScraper] ❌ Xiaohongshu search failed for keyword: "${keyword}"`, error);
            throw error; // Propagate error
        }
    }
    /**
     * 检测淘宝登录状态
     */
    async checkTaobaoLoginStatus() {
        try {
            // 检查是否存在用户头像或登录按钮
            const userAvatar = await this.page.$(SELECTORS.taobao.userAvatar);
            const loginForm = await this.page.$(SELECTORS.taobao.loginForm);
            if (userAvatar) {
                console.log('[PlatformScraper] ✅ 检测到已登录状态');
                return false; // 已登录，不需要登录
            }
            if (loginForm) {
                console.log('[PlatformScraper] ⚠️  检测到登录表单，需要登录');
                return true; // 需要登录
            }
            // 尝试其他检测方法
            const pageText = await this.page.textContent('body');
            if (pageText && (pageText.includes('登录') || pageText.includes('登陆'))) {
                console.log('[PlatformScraper] ⚠️  页面包含登录相关文本，可能需要登录');
                return true;
            }
            return false; // 默认认为不需要登录
        }
        catch (error) {
            console.error('[PlatformScraper] 登录状态检测失败:', error);
            return false; // 检测失败时假设不需要登录
        }
    }
    /**
     * 等待用户完成淘宝登录
     */
    async waitForTaobaoLogin() {
        console.log('[PlatformScraper] 等待用户完成登录...');
        // 最多等待5分钟让用户完成登录
        const maxWaitTime = 5 * 60 * 1000; // 5分钟
        const startTime = Date.now();
        while (Date.now() - startTime < maxWaitTime) {
            try {
                // 检查是否已经登录成功
                const userAvatar = await this.page.$(SELECTORS.taobao.userAvatar);
                if (userAvatar) {
                    console.log('[PlatformScraper] ✅ 用户登录成功！');
                    return;
                }
                // 检查是否有验证码需要处理
                const captcha = await this.page.$(SELECTORS.taobao.captchaSlider);
                if (captcha) {
                    console.log('[PlatformScraper] ⚠️  检测到验证码，等待用户处理...');
                }
                await this.page.waitForTimeout(2000); // 每2秒检查一次
            }
            catch (error) {
                console.error('[PlatformScraper] 登录等待过程中出错:', error);
                await this.page.waitForTimeout(2000);
            }
        }
        console.log('[PlatformScraper] ⚠️  登录等待超时，继续尝试搜索...');
    }
    /**
     * 检测小红书登录状态
     */
    async checkXiaohongshuLoginStatus() {
        try {
            // 检查是否存在用户信息或登录按钮
            const userProfile = await this.page.$(SELECTORS.xiaohongshu.userProfile);
            const loginButton = await this.page.$(SELECTORS.xiaohongshu.loginButton);
            if (userProfile) {
                console.log('[PlatformScraper] ✅ 检测到小红书已登录状态');
                return false; // 已登录，不需要登录
            }
            if (loginButton) {
                console.log('[PlatformScraper] ⚠️  检测到小红书登录按钮，需要登录');
                return true; // 需要登录
            }
            // 尝试其他检测方法
            const pageText = await this.page.textContent('body');
            if (pageText && (pageText.includes('登录') || pageText.includes('login'))) {
                console.log('[PlatformScraper] ⚠️  页面包含登录相关文本，可能需要登录');
                return true;
            }
            return false; // 默认认为不需要登录
        }
        catch (error) {
            console.error('[PlatformScraper] 小红书登录状态检测失败:', error);
            return false; // 检测失败时假设不需要登录
        }
    }
    /**
     * 等待用户完成小红书登录
     */
    async waitForXiaohongshuLogin() {
        console.log('[PlatformScraper] 等待用户完成小红书登录...');
        // 最多等待5分钟让用户完成登录
        const maxWaitTime = 5 * 60 * 1000; // 5分钟
        const startTime = Date.now();
        while (Date.now() - startTime < maxWaitTime) {
            try {
                // 检查是否已经登录成功
                const userProfile = await this.page.$(SELECTORS.xiaohongshu.userProfile);
                if (userProfile) {
                    console.log('[PlatformScraper] ✅ 用户小红书登录成功！');
                    return;
                }
                // 检查登录按钮是否还存在
                const loginButton = await this.page.$(SELECTORS.xiaohongshu.loginButton);
                if (!loginButton) {
                    console.log('[PlatformScraper] ✅ 登录按钮消失，可能已登录');
                    return;
                }
                await this.page.waitForTimeout(2000); // 每2秒检查一次
            }
            catch (error) {
                console.error('[PlatformScraper] 小红书登录等待过程中出错:', error);
                await this.page.waitForTimeout(2000);
            }
        }
        console.log('[PlatformScraper] ⚠️  小红书登录等待超时，继续尝试搜索...');
    }
    /**
     * 🚀 修复：切换到销量排序
     */
    async switchToSalesSorting() {
        try {
            console.log('[PlatformScraper] 🎯 开始设置淘宝销量排序...');

            // 🔥 策略1：首先检查URL是否已经包含销量排序参数
            const currentUrl = this.page.url();
            if (currentUrl.includes('sort=sale-desc')) {
                console.log('[PlatformScraper] ✅ URL已包含销量排序参数，排序已生效');
                return;
            }

            // 🔥 策略2：直接通过URL参数设置排序（最可靠的方法）
            console.log('[PlatformScraper] 🔄 通过URL参数强制设置销量排序');
            console.log(`[PlatformScraper] 原URL: ${currentUrl}`);

            const newUrl = currentUrl.includes('?')
                ? `${currentUrl}&sort=sale-desc`
                : `${currentUrl}?sort=sale-desc`;

            console.log(`[PlatformScraper] 新URL: ${newUrl}`);

            await this.page.goto(newUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
            await this.page.waitForTimeout(3000);

            // 🔥 策略3：验证排序是否真正生效
            const finalUrl = this.page.url();
            if (finalUrl.includes('sort=sale-desc')) {
                console.log('[PlatformScraper] ✅ 销量排序设置成功，URL验证通过');

                // 额外验证：检查页面内容是否反映了销量排序
                try {
                    // 等待商品列表加载
                    await this.page.waitForSelector('a[href*="item.htm"]', { timeout: 10000 });

                    // 检查是否有销量相关的文本显示
                    const pageContent = await this.page.content();
                    if (pageContent.includes('销量') || pageContent.includes('sale')) {
                        console.log('[PlatformScraper] ✅ 页面内容确认销量排序生效');
                    }
                } catch (e) {
                    console.log('[PlatformScraper] ⚠️ 无法验证页面内容，但URL参数已设置');
                }

                return;
            } else {
                console.log('[PlatformScraper] ⚠️ URL参数设置后未生效，可能被重定向');

                // 🔥 策略4：如果URL参数被重定向，尝试点击排序按钮
                console.log('[PlatformScraper] 🔄 尝试通过点击排序按钮设置销量排序');

                // 🎯 根据实际页面结构使用正确的选择器
                const salesSortSelectors = [
                    'tab:has-text("销量")',           // 淘宝使用tab元素
                    '[role="tab"]:has-text("销量")',  // 备用tab选择器
                    'generic:has-text("销量")',       // tab内的generic元素
                    'div:has-text("销量")',           // 传统div选择器
                    'span:has-text("销量")',          // 传统span选择器
                    'a:has-text("销量")',             // 传统a选择器
                    '[data-value="sale-desc"]'        // 数据属性选择器
                ];

                for (const selector of salesSortSelectors) {
                    try {
                        console.log(`[PlatformScraper] 🔍 尝试选择器: ${selector}`);
                        const element = await this.page.$(selector);
                        if (element && await element.isVisible()) {
                            console.log(`[PlatformScraper] 🎯 找到销量排序元素: ${selector}`);
                            await element.click();
                            console.log(`[PlatformScraper] ✅ 通过选择器 "${selector}" 点击销量排序`);
                            await this.page.waitForTimeout(3000);

                            // 再次验证URL
                            const updatedUrl = this.page.url();
                            if (updatedUrl.includes('sort=sale-desc')) {
                                console.log('[PlatformScraper] ✅ 点击后销量排序生效');
                                return;
                            } else {
                                console.log(`[PlatformScraper] ⚠️ 点击后URL未变化: ${updatedUrl}`);
                            }
                        } else {
                            console.log(`[PlatformScraper] ⚠️ 选择器 "${selector}" 未找到可见元素`);
                        }
                    } catch (e) {
                        console.log(`[PlatformScraper] ❌ 选择器 "${selector}" 失败:`, e.message);
                    }
                }

                console.log('[PlatformScraper] ⚠️ 所有排序方法都未能在URL中生效，但继续执行');
            }

        } catch (error) {
            console.error('[PlatformScraper] ❌ 销量排序设置失败:', error.message);
            console.log('[PlatformScraper] 🔄 继续使用默认排序');
        }
    }
    /**
     * 尝试切换到最热排序（小红书）
     * 基于实际浏览器测试，排序选项位于页面底部的listitem标签中
     */
    /**
     * 🚀 智能排序切换（小红书）- 支持"最热"和"最多评论"动态适配
     * 这是小红书反爬虫策略之一，有时显示"最热"，有时显示"最多评论"
     */
    async switchToHotSorting() {
        console.log('[PlatformScraper] 🎯 开始设置小红书最热排序...');

        try {
            // 等待页面加载
            await this.page.waitForTimeout(2000);

            // 🔥 策略1：检查URL是否已经包含最热排序参数
            const currentUrl = this.page.url();
            if (currentUrl.includes('sort_type=hot') || currentUrl.includes('sort=hot') || currentUrl.includes('type=51')) {
                console.log('[PlatformScraper] ✅ URL已包含最热排序参数，排序已生效');
                return;
            }

            // 🔥 策略2：尝试通过URL参数设置最热排序
            console.log('[PlatformScraper] 🔄 尝试通过URL参数设置最热排序');

            // 小红书的最热排序通常使用 type=51 参数
            let newUrl;
            if (currentUrl.includes('type=')) {
                // 替换现有的type参数
                newUrl = currentUrl.replace(/type=\d+/, 'type=51');
            } else {
                // 添加type参数
                newUrl = currentUrl.includes('?')
                    ? `${currentUrl}&type=51`
                    : `${currentUrl}?type=51`;
            }

            if (newUrl !== currentUrl) {
                try {
                    console.log(`[PlatformScraper] 原URL: ${currentUrl}`);
                    console.log(`[PlatformScraper] 新URL: ${newUrl}`);

                    await this.page.goto(newUrl, { waitUntil: 'domcontentloaded', timeout: 15000 });
                    await this.page.waitForTimeout(3000);

                    const finalUrl = this.page.url();
                    if (finalUrl.includes('type=51') || finalUrl.includes('sort=hot')) {
                        console.log('[PlatformScraper] ✅ 通过URL参数成功设置最热排序');
                        return;
                    }
                } catch (e) {
                    console.log('[PlatformScraper] ⚠️ URL参数方法失败，尝试点击排序');
                }
            }

            // 🔥 策略3：通过点击排序选项设置
            console.log('[PlatformScraper] 🔄 尝试通过点击设置最热排序');

            // 等待排序选项加载
            await this.page.waitForSelector('listitem, button, [role="tab"], .sort-option', { timeout: 5000 }).catch(() => {
                console.log('[PlatformScraper] 未找到排序选项元素');
            });

            // 定义优先级排序选项（最热 > 热度 > 最多评论）
            const targetSortOptions = ['最热', '热度', '最多评论', '评论数', '互动量'];

            // 检查当前排序状态
            try {
                for (const sortOption of targetSortOptions) {
                    const selectors = [
                        `listitem:has-text("${sortOption}")`,
                        `button:has-text("${sortOption}")`,
                        `[role="tab"]:has-text("${sortOption}")`
                    ];

                    for (const selector of selectors) {
                        try {
                            const currentElement = await this.page.$(selector);
                            if (currentElement) {
                                const hasSelectedClass = await currentElement.evaluate(el =>
                                    el.querySelector('img') !== null ||
                                    el.classList.contains('selected') ||
                                    el.classList.contains('active') ||
                                    el.getAttribute('aria-selected') === 'true' ||
                                    el.style.color === 'rgb(255, 45, 85)' // 小红书的选中颜色
                                );
                                if (hasSelectedClass) {
                                    console.log(`[PlatformScraper] ✅ 已经是"${sortOption}"排序`);
                                    return;
                                }
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                }
            } catch (e) {
                console.log('[PlatformScraper] 无法检测当前排序状态，继续尝试切换');
            }

            // 按优先级尝试切换排序
            for (const sortOption of targetSortOptions) {
                try {
                    console.log(`[PlatformScraper] 🔍 尝试切换到"${sortOption}"排序`);

                    // 尝试多种选择器
                    const selectors = [
                        `listitem:has-text("${sortOption}")`,
                        `button:has-text("${sortOption}")`,
                        `[role="tab"]:has-text("${sortOption}")`,
                        `div:has-text("${sortOption}")`,
                        `span:has-text("${sortOption}")`
                    ];

                    let sortElement = null;
                    for (const selector of selectors) {
                        try {
                            sortElement = await this.page.$(selector);
                            if (sortElement && await sortElement.isVisible()) {
                                console.log(`[PlatformScraper] 🎯 找到排序元素: ${selector}`);
                                break;
                            }
                        } catch (e) {
                            continue;
                        }
                    }

                    // 如果没找到，尝试精确文本匹配
                    if (!sortElement) {
                        console.log(`[PlatformScraper] 🔍 使用精确文本匹配查找"${sortOption}"`);
                        const allElements = await this.page.$$('button, a, div, span, listitem, [role="tab"]');
                        for (const element of allElements) {
                            try {
                                const text = await element.textContent();
                                if (text && text.trim() === sortOption && await element.isVisible()) {
                                    sortElement = element;
                                    console.log(`[PlatformScraper] 🎯 通过文本匹配找到"${sortOption}"`);
                                    break;
                                }
                            } catch (e) {
                                continue;
                            }
                        }
                    }

                    if (sortElement) {
                        await sortElement.click();
                        console.log(`[PlatformScraper] ✅ 成功点击"${sortOption}"排序选项`);
                        await this.page.waitForTimeout(3000);

                        // 验证排序是否生效
                        try {
                            await this.page.waitForSelector(SELECTORS.xiaohongshu.searchResultItem, { timeout: 10000 });
                            console.log(`[PlatformScraper] ✅ "${sortOption}"排序页面加载完成`);

                            // 检查URL是否有变化
                            const updatedUrl = this.page.url();
                            if (updatedUrl !== currentUrl) {
                                console.log(`[PlatformScraper] ✅ URL已更新，排序生效: ${updatedUrl}`);
                            }

                            return;
                        } catch (e) {
                            console.log(`[PlatformScraper] ⚠️ "${sortOption}"排序页面加载超时，但继续执行`);
                            return;
                        }
                    } else {
                        console.log(`[PlatformScraper] ⚠️ 未找到"${sortOption}"排序选项`);
                    }
                } catch (e) {
                    console.log(`[PlatformScraper] ❌ "${sortOption}"排序切换失败:`, e.message);
                }
            }

            console.log('[PlatformScraper] ⚠️ 所有排序选项都未能成功设置，使用默认排序');

        } catch (error) {
            console.error('[PlatformScraper] ❌ 小红书排序设置失败:', error.message);
            console.log('[PlatformScraper] 🔄 继续使用默认排序');
        }
    }
    /**
     * Scrapes comments from the current page.
     * Assumes the page is already on a product/post details view.
     * @returns {Promise<string[]>} - An array of comment texts.
     */
    async scrapeComments() {
        try {
            if (this.platform === 'taobao') {
                await this.page.click(SELECTORS.taobao.commentTab);
                await this.page.waitForSelector(this.selectors.commentItem, { timeout: 20000 });
            }
            else {
                await this.page.waitForSelector(this.selectors.commentItem, { timeout: 20000 });
            }
            // This is a common pattern: scroll down to load more comments.
            for (let i = 0; i < 5; i++) {
                await this.page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
                await this.page.waitForTimeout(1000); // Wait for content to load
            }
            const comments = await this.page.$$eval(this.selectors.commentItem, (items, selector) => items.map((item) => item.querySelector(selector)?.innerText).filter(Boolean), this.selectors.commentContent);
            return comments;
        }
        catch (error) {
            console.error(`[PlatformScraper] Could not scrape comments from ${this.page.url()}:`, error);
            return []; // Return empty array on failure
        }
    }
}
exports.PlatformScraper = PlatformScraper;
